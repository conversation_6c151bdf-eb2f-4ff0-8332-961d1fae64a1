import Link from "next/link";
import Image from "next/image";

export default function Home() {
  return (
    <div className="min-h-screen bg-black text-white flex items-center justify-center px-4">
      <div className="text-center max-w-md mx-auto animate-fade-in">
        {/* Profile Image */}
        <div className="mb-8 animate-slide-in-down">
          <Image
            src="/profile.png"
            alt="g0tt - foto de perfil"
            width={200}
            height={200}
            className="rounded-full mx-auto border-4 border-white shadow-lg"
            priority
          />
        </div>

        {/* Name */}
        <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-8 animate-slide-in-up">g0tt</h1>

        {/* Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center animate-slide-in-up">
          <Link
            href="/downloads"
            className="inline-block bg-white text-black px-6 py-3 sm:px-8 sm:py-3 text-base sm:text-lg font-medium btn-primary"
          >
            downloads
          </Link>
          <Link
            href="/sobre"
            className="inline-block border border-white text-white px-6 py-3 sm:px-8 sm:py-3 text-base sm:text-lg font-medium btn-secondary"
          >
            sobre
          </Link>
        </div>
      </div>
    </div>
  );
}
