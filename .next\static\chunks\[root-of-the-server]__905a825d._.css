/* [next]/internal/font/google/source_code_pro_3c9a9855.module.css [app-client] (css) */
@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlMOvWnsUnxlC9-s.3b795400.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlOevWnsUnxlC9-s.e37f9eb1.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlMevWnsUnxlC9-s.74007271.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlPuvWnsUnxlC9-s.11207b0d.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlMuvWnsUnxlC9-s.b49258b5.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlM_vWnsUnxlC9-s.27423199.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 300;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlPevWnsUnxg-s.p.1f4f4aaa.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlMOvWnsUnxlC9-s.3b795400.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlOevWnsUnxlC9-s.e37f9eb1.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlMevWnsUnxlC9-s.74007271.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlPuvWnsUnxlC9-s.11207b0d.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlMuvWnsUnxlC9-s.b49258b5.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlM_vWnsUnxlC9-s.27423199.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlPevWnsUnxg-s.p.1f4f4aaa.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlMOvWnsUnxlC9-s.3b795400.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlOevWnsUnxlC9-s.e37f9eb1.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlMevWnsUnxlC9-s.74007271.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlPuvWnsUnxlC9-s.11207b0d.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlMuvWnsUnxlC9-s.b49258b5.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlM_vWnsUnxlC9-s.27423199.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlPevWnsUnxg-s.p.1f4f4aaa.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlMOvWnsUnxlC9-s.3b795400.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlOevWnsUnxlC9-s.e37f9eb1.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlMevWnsUnxlC9-s.74007271.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlPuvWnsUnxlC9-s.11207b0d.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlMuvWnsUnxlC9-s.b49258b5.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlM_vWnsUnxlC9-s.27423199.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlPevWnsUnxg-s.p.1f4f4aaa.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlMOvWnsUnxlC9-s.3b795400.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlOevWnsUnxlC9-s.e37f9eb1.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlMevWnsUnxlC9-s.74007271.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlPuvWnsUnxlC9-s.11207b0d.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlMuvWnsUnxlC9-s.b49258b5.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlM_vWnsUnxlC9-s.27423199.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Source Code Pro;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/HI_SiYsKILxRpg3hIP6sJ7fM7PqlPevWnsUnxg-s.p.1f4f4aaa.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Source Code Pro Fallback;
  src: local(Arial);
  ascent-override: 73.11%;
  descent-override: 20.28%;
  line-gap-override: 0.0%;
  size-adjust: 134.59%;
}

.source_code_pro_3c9a9855-module__wB9D2a__className {
  font-family: Source Code Pro, Source Code Pro Fallback;
  font-style: normal;
}

.source_code_pro_3c9a9855-module__wB9D2a__variable {
  --font-source-code-pro: "Source Code Pro", "Source Code Pro Fallback";
}


/* [project]/src/app/globals.css [app-client] (css) */
@layer properties {
  @supports (((-webkit-hyphens: none)) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color: rgb(from red r g b)))) {
    *, :before, :after, ::backdrop {
      --tw-space-y-reverse: 0;
      --tw-border-style: solid;
      --tw-leading: initial;
      --tw-font-weight: initial;
      --tw-shadow: 0 0 #0000;
      --tw-shadow-color: initial;
      --tw-shadow-alpha: 100%;
      --tw-inset-shadow: 0 0 #0000;
      --tw-inset-shadow-color: initial;
      --tw-inset-shadow-alpha: 100%;
      --tw-ring-color: initial;
      --tw-ring-shadow: 0 0 #0000;
      --tw-inset-ring-color: initial;
      --tw-inset-ring-shadow: 0 0 #0000;
      --tw-ring-inset: initial;
      --tw-ring-offset-width: 0px;
      --tw-ring-offset-color: #fff;
      --tw-ring-offset-shadow: 0 0 #0000;
    }
  }
}

@layer theme {
  :root, :host {
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-mono: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
    --color-gray-300: oklch(87.2% .01 258.338);
    --color-gray-400: oklch(70.7% .022 261.325);
    --color-gray-700: oklch(37.3% .034 259.733);
    --color-gray-800: oklch(27.8% .033 256.848);
    --color-black: #000;
    --color-white: #fff;
    --spacing: .25rem;
    --container-md: 28rem;
    --container-4xl: 56rem;
    --text-sm: .875rem;
    --text-sm--line-height: calc(1.25 / .875);
    --text-base: 1rem;
    --text-base--line-height: calc(1.5 / 1);
    --text-lg: 1.125rem;
    --text-lg--line-height: calc(1.75 / 1.125);
    --text-2xl: 1.5rem;
    --text-2xl--line-height: calc(2 / 1.5);
    --text-4xl: 2.25rem;
    --text-4xl--line-height: calc(2.5 / 2.25);
    --text-5xl: 3rem;
    --text-5xl--line-height: 1;
    --text-6xl: 3.75rem;
    --text-6xl--line-height: 1;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    --leading-relaxed: 1.625;
    --default-transition-duration: .15s;
    --default-transition-timing-function: cubic-bezier(.4, 0, .2, 1);
    --default-font-family: var(--font-sans);
    --default-mono-font-family: var(--font-mono);
  }
}

@layer base {
  *, :after, :before, ::backdrop, ::file-selector-button {
    box-sizing: border-box;
    border: 0 solid;
    margin: 0;
    padding: 0;
  }

  html, :host {
    -webkit-text-size-adjust: 100%;
    tab-size: 4;
    line-height: 1.5;
    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji");
    font-feature-settings: var(--default-font-feature-settings, normal);
    font-variation-settings: var(--default-font-variation-settings, normal);
    -webkit-tap-highlight-color: transparent;
  }

  hr {
    height: 0;
    color: inherit;
    border-top-width: 1px;
  }

  abbr:where([title]) {
    text-decoration: underline dotted;
  }

  h1, h2, h3, h4, h5, h6 {
    font-size: inherit;
    font-weight: inherit;
  }

  a {
    color: inherit;
    -webkit-text-decoration: inherit;
    text-decoration: inherit;
  }

  b, strong {
    font-weight: bolder;
  }

  code, kbd, samp, pre {
    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace);
    font-feature-settings: var(--default-mono-font-feature-settings, normal);
    font-variation-settings: var(--default-mono-font-variation-settings, normal);
    font-size: 1em;
  }

  small {
    font-size: 80%;
  }

  sub, sup {
    vertical-align: baseline;
    font-size: 75%;
    line-height: 0;
    position: relative;
  }

  sub {
    bottom: -.25em;
  }

  sup {
    top: -.5em;
  }

  table {
    text-indent: 0;
    border-color: inherit;
    border-collapse: collapse;
  }

  :-moz-focusring {
    outline: auto;
  }

  progress {
    vertical-align: baseline;
  }

  summary {
    display: list-item;
  }

  ol, ul, menu {
    list-style: none;
  }

  img, svg, video, canvas, audio, iframe, embed, object {
    vertical-align: middle;
    display: block;
  }

  img, video {
    max-width: 100%;
    height: auto;
  }

  button, input, select, optgroup, textarea, ::file-selector-button {
    font: inherit;
    font-feature-settings: inherit;
    font-variation-settings: inherit;
    letter-spacing: inherit;
    color: inherit;
    opacity: 1;
    background-color: #0000;
    border-radius: 0;
  }

  :where(select:is([multiple], [size])) optgroup {
    font-weight: bolder;
  }

  :where(select:is([multiple], [size])) optgroup option {
    padding-inline-start: 20px;
  }

  ::file-selector-button {
    margin-inline-end: 4px;
  }

  ::placeholder {
    opacity: 1;
  }

  @supports (not ((-webkit-appearance: -apple-pay-button))) or (contain-intrinsic-size: 1px) {
    ::placeholder {
      color: currentColor;
    }

    @supports (color: color-mix(in lab, red, red)) {
      ::placeholder {
        color: color-mix(in oklab, currentcolor 50%, transparent);
      }
    }
  }

  textarea {
    resize: vertical;
  }

  ::-webkit-search-decoration {
    -webkit-appearance: none;
  }

  ::-webkit-date-and-time-value {
    min-height: 1lh;
    text-align: inherit;
  }

  ::-webkit-datetime-edit {
    display: inline-flex;
  }

  ::-webkit-datetime-edit-fields-wrapper {
    padding: 0;
  }

  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {
    padding-block: 0;
  }

  :-moz-ui-invalid {
    box-shadow: none;
  }

  button, input:where([type="button"], [type="reset"], [type="submit"]), ::file-selector-button {
    appearance: button;
  }

  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {
    height: auto;
  }

  [hidden]:where(:not([hidden="until-found"])) {
    display: none !important;
  }
}

@layer components;

@layer utilities {
  .mx-auto {
    margin-inline: auto;
  }

  .mt-20 {
    margin-top: calc(var(--spacing) * 20);
  }

  .mb-3 {
    margin-bottom: calc(var(--spacing) * 3);
  }

  .mb-4 {
    margin-bottom: calc(var(--spacing) * 4);
  }

  .mb-6 {
    margin-bottom: calc(var(--spacing) * 6);
  }

  .mb-8 {
    margin-bottom: calc(var(--spacing) * 8);
  }

  .mb-12 {
    margin-bottom: calc(var(--spacing) * 12);
  }

  .mb-16 {
    margin-bottom: calc(var(--spacing) * 16);
  }

  .block {
    display: block;
  }

  .flex {
    display: flex;
  }

  .inline-block {
    display: inline-block;
  }

  .min-h-screen {
    min-height: 100vh;
  }

  .max-w-4xl {
    max-width: var(--container-4xl);
  }

  .max-w-md {
    max-width: var(--container-md);
  }

  .max-w-none {
    max-width: none;
  }

  .flex-shrink-0 {
    flex-shrink: 0;
  }

  .flex-wrap {
    flex-wrap: wrap;
  }

  .items-center {
    align-items: center;
  }

  .justify-center {
    justify-content: center;
  }

  .gap-4 {
    gap: calc(var(--spacing) * 4);
  }

  :where(.space-y-2 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-4 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));
  }

  :where(.space-y-8 > :not(:last-child)) {
    --tw-space-y-reverse: 0;
    margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));
    margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));
  }

  .rounded-full {
    border-radius: 3.40282e38px;
  }

  .border {
    border-style: var(--tw-border-style);
    border-width: 1px;
  }

  .border-4 {
    border-style: var(--tw-border-style);
    border-width: 4px;
  }

  .border-t {
    border-top-style: var(--tw-border-style);
    border-top-width: 1px;
  }

  .border-l-4 {
    border-left-style: var(--tw-border-style);
    border-left-width: 4px;
  }

  .border-gray-700 {
    border-color: var(--color-gray-700);
  }

  .border-white {
    border-color: var(--color-white);
  }

  .bg-black {
    background-color: var(--color-black);
  }

  .bg-white {
    background-color: var(--color-white);
  }

  .px-4 {
    padding-inline: calc(var(--spacing) * 4);
  }

  .px-6 {
    padding-inline: calc(var(--spacing) * 6);
  }

  .py-3 {
    padding-block: calc(var(--spacing) * 3);
  }

  .py-12 {
    padding-block: calc(var(--spacing) * 12);
  }

  .pt-8 {
    padding-top: calc(var(--spacing) * 8);
  }

  .pl-4 {
    padding-left: calc(var(--spacing) * 4);
  }

  .pl-6 {
    padding-left: calc(var(--spacing) * 6);
  }

  .text-center {
    text-align: center;
  }

  .text-2xl {
    font-size: var(--text-2xl);
    line-height: var(--tw-leading, var(--text-2xl--line-height));
  }

  .text-4xl {
    font-size: var(--text-4xl);
    line-height: var(--tw-leading, var(--text-4xl--line-height));
  }

  .text-base {
    font-size: var(--text-base);
    line-height: var(--tw-leading, var(--text-base--line-height));
  }

  .text-sm {
    font-size: var(--text-sm);
    line-height: var(--tw-leading, var(--text-sm--line-height));
  }

  .leading-relaxed {
    --tw-leading: var(--leading-relaxed);
    line-height: var(--leading-relaxed);
  }

  .font-bold {
    --tw-font-weight: var(--font-weight-bold);
    font-weight: var(--font-weight-bold);
  }

  .font-medium {
    --tw-font-weight: var(--font-weight-medium);
    font-weight: var(--font-weight-medium);
  }

  .font-semibold {
    --tw-font-weight: var(--font-weight-semibold);
    font-weight: var(--font-weight-semibold);
  }

  .text-black {
    color: var(--color-black);
  }

  .text-gray-300 {
    color: var(--color-gray-300);
  }

  .text-gray-400 {
    color: var(--color-gray-400);
  }

  .text-white {
    color: var(--color-white);
  }

  .antialiased {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  .shadow-lg {
    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, #0000001a), 0 4px 6px -4px var(--tw-shadow-color, #0000001a);
    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);
  }

  .transition-colors {
    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;
    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));
    transition-duration: var(--tw-duration, var(--default-transition-duration));
  }

  @media (hover: hover) {
    .hover\:bg-gray-300:hover {
      background-color: var(--color-gray-300);
    }
  }

  @media (hover: hover) {
    .hover\:bg-gray-800:hover {
      background-color: var(--color-gray-800);
    }
  }

  @media (hover: hover) {
    .hover\:text-white:hover {
      color: var(--color-white);
    }
  }

  @media (hover: hover) {
    .hover\:underline:hover {
      text-decoration-line: underline;
    }
  }

  @media (width >= 40rem) {
    .sm\:px-8 {
      padding-inline: calc(var(--spacing) * 8);
    }
  }

  @media (width >= 40rem) {
    .sm\:py-3 {
      padding-block: calc(var(--spacing) * 3);
    }
  }

  @media (width >= 40rem) {
    .sm\:text-5xl {
      font-size: var(--text-5xl);
      line-height: var(--tw-leading, var(--text-5xl--line-height));
    }
  }

  @media (width >= 40rem) {
    .sm\:text-lg {
      font-size: var(--text-lg);
      line-height: var(--tw-leading, var(--text-lg--line-height));
    }
  }

  @media (width >= 48rem) {
    .md\:text-6xl {
      font-size: var(--text-6xl);
      line-height: var(--tw-leading, var(--text-6xl--line-height));
    }
  }
}

:root {
  --font-sans: var(--font-source-code-pro);
}

* {
  box-sizing: border-box;
  font-family: var(--font-source-code-pro), monospace !important;
}

body {
  color: #fff;
  background: #000;
  line-height: 1.6;
  font-family: var(--font-source-code-pro), monospace !important;
}

h1, h2, h3, h4, h5, h6, p, a, button, div, span, li, ul, ol {
  font-family: var(--font-source-code-pro), monospace !important;
}

html {
  scroll-behavior: smooth;
}

a:focus, button:focus {
  outline-offset: 2px;
  outline: 2px solid #fff;
}

::selection {
  color: #0a0a0a;
  background: #fff;
}

@property --tw-space-y-reverse {
  syntax: "*";
  inherits: false;
  initial-value: 0;
}

@property --tw-border-style {
  syntax: "*";
  inherits: false;
  initial-value: solid;
}

@property --tw-leading {
  syntax: "*";
  inherits: false
}

@property --tw-font-weight {
  syntax: "*";
  inherits: false
}

@property --tw-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-inset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-shadow-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-shadow-alpha {
  syntax: "<percentage>";
  inherits: false;
  initial-value: 100%;
}

@property --tw-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-inset-ring-color {
  syntax: "*";
  inherits: false
}

@property --tw-inset-ring-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}

@property --tw-ring-inset {
  syntax: "*";
  inherits: false
}

@property --tw-ring-offset-width {
  syntax: "<length>";
  inherits: false;
  initial-value: 0;
}

@property --tw-ring-offset-color {
  syntax: "*";
  inherits: false;
  initial-value: #fff;
}

@property --tw-ring-offset-shadow {
  syntax: "*";
  inherits: false;
  initial-value: 0 0 #0000;
}


/*# sourceMappingURL=%5Broot-of-the-server%5D__905a825d._.css.map*/