import Link from "next/link";
import Image from "next/image";

export default function Home() {
  return (
    <div className="min-h-screen bg-black text-white flex items-center justify-center px-4">
      <div className="text-center max-w-md mx-auto">
        {/* Profile Image */}
        <div className="mb-8">
          <Image
            src="/profile.png"
            alt="g0tt - foto de perfil"
            width={200}
            height={200}
            className="rounded-full mx-auto border-4 border-white shadow-lg"
            priority
          />
        </div>

        {/* Name */}
        <h1 className="text-4xl sm:text-5xl md:text-6xl font-bold mb-8">g0tt</h1>

        {/* Download Button */}
        <Link
          href="/downloads"
          className="inline-block bg-white text-black px-6 py-3 sm:px-8 sm:py-3 text-base sm:text-lg font-medium hover:bg-gray-300 transition-colors"
        >
          downloads
        </Link>
      </div>
    </div>
  );
}
