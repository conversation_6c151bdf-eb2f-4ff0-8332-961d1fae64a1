{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/g0tt-website/src/app/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport Image from \"next/image\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-black text-white flex items-center justify-center px-4\">\n      <div className=\"text-center max-w-md mx-auto\">\n        {/* Profile Image */}\n        <div className=\"mb-8\">\n          <Image\n            src=\"/profile.png\"\n            alt=\"g0tt - foto de perfil\"\n            width={200}\n            height={200}\n            className=\"rounded-full mx-auto border-4 border-white shadow-lg\"\n            priority\n          />\n        </div>\n\n        {/* Name */}\n        <h1 className=\"text-4xl sm:text-5xl md:text-6xl font-bold mb-8\">g0tt</h1>\n\n        {/* Download Button */}\n        <Link\n          href=\"/downloads\"\n          className=\"inline-block bg-white text-black px-6 py-3 sm:px-8 sm:py-3 text-base sm:text-lg font-medium hover:bg-gray-300 transition-colors\"\n        >\n          downloads\n        </Link>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wBACJ,KAAI;wBACJ,KAAI;wBACJ,OAAO;wBACP,QAAQ;wBACR,WAAU;wBACV,QAAQ;;;;;;;;;;;8BAKZ,8OAAC;oBAAG,WAAU;8BAAkD;;;;;;8BAGhE,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;8BACX;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}]}