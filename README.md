# g0tt - Site do Canal de Computação

Site oficial do canal **g0tt** no YouTube, focado em computação, programação e ferramentas de desenvolvimento.

## 🎯 Sobre o Projeto

Este é um site minimalista e text-based que disponibiliza os principais recursos do canal g0tt: Debloat Pack para otimização do Windows e arquivo Autounattend.xml para instalação automatizada.

## ✨ características

- **design minimalista**: focado em conteúdo, sem distrações
- **preto e branco**: paleta de cores monocromática elegante
- **text-based**: prioriza a informação sobre elementos visuais
- **responsivo**: funciona perfeitamente em desktop e mobile
- **rápido**: construído com next.js e otimizado para performance
- **acessível**: seguindo boas práticas de acessibilidade web

## 📁 Estrutura do Site

- **Página Inicial**: Design minimalista com apenas o nome "g0tt" e botão para downloads
- **Downloads**: Links diretos para Debloat Pack e arquivo Autounattend.xml com ícones personalizados
- **Sobre**: Informações sobre o canal e contato

## 🛠️ Tecnologias Utilizadas

- **Next.js 15**: Framework React para produção
- **TypeScript**: Tipagem estática para maior confiabilidade
- **Tailwind CSS**: Framework CSS utilitário
- **Source Code Pro**: Fonte monoespaçada para estética de programação
- **Dark Mode**: Interface escura otimizada

## 🚀 Como Executar

1. Clone o repositório:
```bash
git clone [url-do-repositorio]
cd g0tt-website
```

2. Instale as dependências:
```bash
npm install
```

3. Execute o servidor de desenvolvimento:
```bash
npm run dev
```

4. Abra [http://localhost:3000](http://localhost:3000) no navegador

## 📦 Scripts Disponíveis

- `npm run dev` - Inicia o servidor de desenvolvimento
- `npm run build` - Cria a build de produção
- `npm run start` - Inicia o servidor de produção
- `npm run lint` - Executa o linter

## 🔗 Links Úteis

- [Canal no YouTube](https://youtube.com/@g0tt)
- [Next.js Documentation](https://nextjs.org/docs)
- [Tailwind CSS](https://tailwindcss.com)

## 📝 Licença

Este projeto é de código aberto e está disponível sob a licença MIT.
