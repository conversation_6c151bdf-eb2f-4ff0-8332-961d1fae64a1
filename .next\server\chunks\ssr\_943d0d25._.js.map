{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/g0tt-website/src/app/downloads/page.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport Image from \"next/image\";\n\nexport default function Downloads() {\n  return (\n    <div className=\"min-h-screen bg-black text-white\">\n      <div className=\"max-w-4xl mx-auto px-6 py-12 animate-fade-in\">\n        {/* Header */}\n        <header className=\"mb-16 animate-slide-in-down\">\n          <Link href=\"/\" className=\"text-sm text-gray-400 hover:text-white hover:underline mb-4 block\">\n            ← voltar ao início\n          </Link>\n          <h1 className=\"text-4xl font-bold mb-4\">downloads</h1>\n        </header>\n\n        {/* Downloads */}\n        <main className=\"space-y-8 animate-slide-in-up\">\n          <div className=\"border-l-4 border-white pl-6\">\n            <div className=\"flex items-center gap-4 mb-3\">\n              <Image\n                src=\"/cartola.ico\"\n                alt=\"debloat pack\"\n                width={32}\n                height={32}\n                className=\"flex-shrink-0\"\n              />\n              <h2 className=\"text-2xl font-semibold\">debloat pack</h2>\n            </div>\n            <p className=\"text-gray-400 mb-4\">\n              pasta completa com ferramentas para otimização e limpeza do windows\n            </p>\n            <div className=\"flex flex-wrap gap-4\">\n              <a\n                href=\"https://bit.ly/g0tt-debloat-pack\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-block bg-white text-black px-6 py-3 font-medium btn-primary\"\n              >\n                baixar arquivo →\n              </a>\n              <a\n                href=\"https://www.youtube.com/watch?v=VP05HvGproo\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-block border border-white text-white px-6 py-3 font-medium btn-secondary\"\n              >\n                ver vídeo →\n              </a>\n            </div>\n          </div>\n\n          <div className=\"border-l-4 border-white pl-6\">\n            <div className=\"flex items-center gap-4 mb-3\">\n              <Image\n                src=\"/debloat.ico\"\n                alt=\"autounattend.xml\"\n                width={32}\n                height={32}\n                className=\"flex-shrink-0\"\n              />\n              <h2 className=\"text-2xl font-semibold\">autounattend.xml</h2>\n            </div>\n            <p className=\"text-gray-400 mb-4\">\n              arquivo de configuração para instalação automatizada do windows\n            </p>\n            <div className=\"flex flex-wrap gap-4\">\n              <a\n                href=\"https://bit.ly/autounattend-g0tt\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-block bg-white text-black px-6 py-3 font-medium btn-primary\"\n              >\n                baixar arquivo →\n              </a>\n              <a\n                href=\"https://www.youtube.com/watch?v=VP05HvGproo\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-block border border-white text-white px-6 py-3 font-medium btn-secondary\"\n              >\n                ver vídeo →\n              </a>\n              <a\n                href=\"https://schneegans.de/windows/unattend-generator/\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"inline-block border border-white text-white px-6 py-3 font-medium btn-secondary\"\n              >\n                crie o seu →\n              </a>\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAoE;;;;;;sCAG7F,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;;;;;;;8BAI1C,8OAAC;oBAAK,WAAU;;sCACd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDACX;;;;;;;;;;;;;;;;;;sCAML,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;sDAEZ,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;;;;;;;8CAEzC,8OAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}