import Link from "next/link";

export default function Videos() {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-6xl mx-auto px-6 py-12 animate-fade-in">
        {/* Header */}
        <header className="mb-16 animate-slide-in-down">
          <Link href="/" className="text-sm text-gray-400 hover:text-white hover:underline mb-4 block">
            ← voltar ao início
          </Link>
          <h1 className="text-4xl font-bold mb-4">vídeos</h1>
        </header>

        {/* Videos Grid */}
        <main className="animate-slide-in-up">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">

            {/* Video 1 */}
            <div className="border border-gray-700 bg-gray-900 rounded-lg overflow-hidden hover:border-white transition-colors">
              <div className="aspect-video bg-gray-800 flex items-center justify-center">
                <div className="text-gray-400 text-center">
                  <div className="text-4xl mb-2">▶</div>
                  <div className="text-sm">vídeo 1</div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-3">vídeo do canal</h3>
                <p className="text-gray-400 mb-4 text-sm leading-relaxed">
                  conteúdo sobre computação, programação e ferramentas para desenvolvedores.
                  assista para aprender mais sobre tecnologia e otimização de sistemas.
                </p>
                <a
                  href="https://youtu.be/pzr7REzkTyg?si=TBYPJhie68JL2gKf"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block bg-white text-black px-4 py-2 text-sm font-medium btn-primary"
                >
                  assistir vídeo →
                </a>
              </div>
            </div>

            {/* Video 2 */}
            <div className="border border-gray-700 bg-gray-900 rounded-lg overflow-hidden hover:border-white transition-colors">
              <div className="aspect-video bg-gray-800 flex items-center justify-center">
                <div className="text-gray-400 text-center">
                  <div className="text-4xl mb-2">▶</div>
                  <div className="text-sm">vídeo 2</div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-3">vídeo do canal</h3>
                <p className="text-gray-400 mb-4 text-sm leading-relaxed">
                  tutorial prático sobre ferramentas e técnicas de desenvolvimento.
                  aprenda dicas e truques para melhorar sua produtividade.
                </p>
                <a
                  href="https://youtu.be/xjUoL6zep84?si=d9kaDAzcclO9bK-Q"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block bg-white text-black px-4 py-2 text-sm font-medium btn-primary"
                >
                  assistir vídeo →
                </a>
              </div>
            </div>

            {/* Video 3 */}
            <div className="border border-gray-700 bg-gray-900 rounded-lg overflow-hidden hover:border-white transition-colors">
              <div className="aspect-video bg-gray-800 flex items-center justify-center">
                <div className="text-gray-400 text-center">
                  <div className="text-4xl mb-2">▶</div>
                  <div className="text-sm">vídeo 3</div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-3">vídeo do canal</h3>
                <p className="text-gray-400 mb-4 text-sm leading-relaxed">
                  conteúdo educativo sobre computação e tecnologia.
                  explore conceitos avançados e soluções práticas para desenvolvedores.
                </p>
                <a
                  href="https://youtu.be/VP05HvGproo?si=OCSh_r6-EoMz8jGM"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block bg-white text-black px-4 py-2 text-sm font-medium btn-primary"
                >
                  assistir vídeo →
                </a>
              </div>
            </div>

          </div>

          {/* Canal Link */}
          <div className="mt-16 text-center">
            <div className="border-t border-gray-700 pt-8">
              <h2 className="text-2xl font-semibold mb-4">mais vídeos no canal</h2>
              <p className="text-gray-400 mb-6 max-w-2xl mx-auto">
                acesse o canal no youtube para ver todos os vídeos sobre computação, 
                programação, ferramentas e dicas para desenvolvedores.
              </p>
              <a
                href="https://www.youtube.com/@g0tt_4rdi"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block border border-white text-white px-8 py-3 text-lg font-medium btn-secondary"
              >
                acessar canal →
              </a>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
