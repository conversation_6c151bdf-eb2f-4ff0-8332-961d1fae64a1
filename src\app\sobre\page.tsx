import Link from "next/link";

export default function Sobre() {
  const currentYear = new Date().getFullYear();

  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-4xl mx-auto px-6 py-12 animate-fade-in">
        {/* Header */}
        <header className="mb-16 animate-slide-in-down">
          <Link href="/" className="text-sm text-gray-400 hover:text-white hover:underline mb-4 block">
            ← voltar ao início
          </Link>
          <h1 className="text-4xl font-bold mb-4">sobre o g0tt</h1>
        </header>

        {/* Main Content */}
        <main className="prose prose-lg max-w-none animate-slide-in-up">
          <section className="mb-12">
            <h2 className="text-2xl font-semibold mb-6">o canal</h2>
            <div className="space-y-4 text-gray-300 leading-relaxed">
              <p>
                <strong>o canal</strong> é um canal de computação no youtube dedicado a ensinar
                e compartilhar conhecimento sobre tecnologia, programação e ferramentas de desenvolvimento.
              </p>

              <p>
                nosso foco é criar conteúdo prático e direto ao ponto, ajudando tanto iniciantes
                quanto desenvolvedores experientes a descobrir novas ferramentas e aprimorar suas habilidades.
              </p>

              <p>
                todos os softwares e ferramentas apresentados nos vídeos estão disponíveis
                neste site para facilitar o acesso e download.
              </p>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl font-semibold mb-6">conteúdo do canal</h2>
            <div className="space-y-4">
              <div className="border-l-4 border-white pl-4">
                <h3 className="font-medium">dicas e truques</h3>
                <p className="text-gray-400">
                  shortcuts, configurações e otimizações para aumentar sua produtividade.
                </p>
              </div>

              <div className="border-l-4 border-white pl-4">
                <h3 className="font-medium">projetos práticos</h3>
                <p className="text-gray-400">
                  desenvolvimento de projetos reais do início ao fim.
                </p>
              </div>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="text-2xl font-semibold mb-6">contato</h2>
            <div className="space-y-2">
              <a
                href="https://www.youtube.com/@g0tt_4rdi"
                target="_blank"
                rel="noopener noreferrer"
                className="block hover:underline"
              >
                → youtube: @g0tt_4rdi
              </a>
            </div>
          </section>
        </main>

        {/* Footer */}
        <footer className="mt-20 pt-8 border-t border-gray-700">
          <p className="text-gray-400 text-sm">
            © {currentYear} g0tt - canal de computação
          </p>
        </footer>
      </div>
    </div>
  );
}
