import Link from "next/link";

export default function Videos() {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-6xl mx-auto px-6 py-12 animate-fade-in">
        {/* Header */}
        <header className="mb-16 animate-slide-in-down">
          <Link href="/" className="text-sm text-gray-400 hover:text-white hover:underline mb-4 block">
            ← voltar ao início
          </Link>
          <h1 className="text-4xl font-bold mb-4">vídeos</h1>
        </header>

        {/* Videos Grid */}
        <main className="animate-slide-in-up">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            
            {/* Video 1 - Debloat Pack */}
            <div className="border border-gray-700 bg-gray-900 rounded-lg overflow-hidden hover:border-white transition-colors">
              <div className="aspect-video bg-gray-800 flex items-center justify-center">
                <div className="text-gray-400 text-center">
                  <div className="text-4xl mb-2">▶</div>
                  <div className="text-sm">debloat pack</div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-3">windows debloat pack completo</h3>
                <p className="text-gray-400 mb-4 text-sm leading-relaxed">
                  tutorial completo sobre como usar o debloat pack para otimizar e limpar o windows, 
                  removendo bloatware e melhorando a performance do sistema.
                </p>
                <a
                  href="https://www.youtube.com/watch?v=VP05HvGproo"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block bg-white text-black px-4 py-2 text-sm font-medium btn-primary"
                >
                  assistir vídeo →
                </a>
              </div>
            </div>

            {/* Video 2 - Autounattend */}
            <div className="border border-gray-700 bg-gray-900 rounded-lg overflow-hidden hover:border-white transition-colors">
              <div className="aspect-video bg-gray-800 flex items-center justify-center">
                <div className="text-gray-400 text-center">
                  <div className="text-4xl mb-2">▶</div>
                  <div className="text-sm">autounattend.xml</div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-3">instalação automatizada do windows</h3>
                <p className="text-gray-400 mb-4 text-sm leading-relaxed">
                  aprenda a criar e usar arquivos autounattend.xml para automatizar completamente 
                  a instalação do windows com suas configurações personalizadas.
                </p>
                <a
                  href="https://www.youtube.com/watch?v=VP05HvGproo"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block bg-white text-black px-4 py-2 text-sm font-medium btn-primary"
                >
                  assistir vídeo →
                </a>
              </div>
            </div>

            {/* Video 3 - Ferramentas de Desenvolvimento */}
            <div className="border border-gray-700 bg-gray-900 rounded-lg overflow-hidden hover:border-white transition-colors">
              <div className="aspect-video bg-gray-800 flex items-center justify-center">
                <div className="text-gray-400 text-center">
                  <div className="text-4xl mb-2">▶</div>
                  <div className="text-sm">dev tools</div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-3">ferramentas essenciais para desenvolvedores</h3>
                <p className="text-gray-400 mb-4 text-sm leading-relaxed">
                  overview das melhores ferramentas para desenvolvimento, editores de código, 
                  extensões e configurações que aumentam a produtividade.
                </p>
                <a
                  href="https://www.youtube.com/@g0tt_4rdi"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block bg-white text-black px-4 py-2 text-sm font-medium btn-primary"
                >
                  assistir vídeo →
                </a>
              </div>
            </div>

            {/* Video 4 - Otimização de Sistema */}
            <div className="border border-gray-700 bg-gray-900 rounded-lg overflow-hidden hover:border-white transition-colors">
              <div className="aspect-video bg-gray-800 flex items-center justify-center">
                <div className="text-gray-400 text-center">
                  <div className="text-4xl mb-2">▶</div>
                  <div className="text-sm">otimização</div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-3">otimização avançada do windows</h3>
                <p className="text-gray-400 mb-4 text-sm leading-relaxed">
                  técnicas avançadas para otimizar o windows, configurações do registro, 
                  serviços desnecessários e tweaks para máxima performance.
                </p>
                <a
                  href="https://www.youtube.com/@g0tt_4rdi"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block bg-white text-black px-4 py-2 text-sm font-medium btn-primary"
                >
                  assistir vídeo →
                </a>
              </div>
            </div>

            {/* Video 5 - Configuração de Ambiente */}
            <div className="border border-gray-700 bg-gray-900 rounded-lg overflow-hidden hover:border-white transition-colors">
              <div className="aspect-video bg-gray-800 flex items-center justify-center">
                <div className="text-gray-400 text-center">
                  <div className="text-4xl mb-2">▶</div>
                  <div className="text-sm">setup</div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-3">configurando ambiente de desenvolvimento</h3>
                <p className="text-gray-400 mb-4 text-sm leading-relaxed">
                  guia completo para configurar um ambiente de desenvolvimento profissional, 
                  desde a instalação até as configurações avançadas.
                </p>
                <a
                  href="https://www.youtube.com/@g0tt_4rdi"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block bg-white text-black px-4 py-2 text-sm font-medium btn-primary"
                >
                  assistir vídeo →
                </a>
              </div>
            </div>

            {/* Video 6 - Automação e Scripts */}
            <div className="border border-gray-700 bg-gray-900 rounded-lg overflow-hidden hover:border-white transition-colors">
              <div className="aspect-video bg-gray-800 flex items-center justify-center">
                <div className="text-gray-400 text-center">
                  <div className="text-4xl mb-2">▶</div>
                  <div className="text-sm">automação</div>
                </div>
              </div>
              <div className="p-6">
                <h3 className="text-xl font-semibold mb-3">automação com scripts e ferramentas</h3>
                <p className="text-gray-400 mb-4 text-sm leading-relaxed">
                  aprenda a automatizar tarefas repetitivas usando scripts, batch files 
                  e ferramentas de automação para aumentar sua eficiência.
                </p>
                <a
                  href="https://www.youtube.com/@g0tt_4rdi"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block bg-white text-black px-4 py-2 text-sm font-medium btn-primary"
                >
                  assistir vídeo →
                </a>
              </div>
            </div>

          </div>

          {/* Canal Link */}
          <div className="mt-16 text-center">
            <div className="border-t border-gray-700 pt-8">
              <h2 className="text-2xl font-semibold mb-4">mais vídeos no canal</h2>
              <p className="text-gray-400 mb-6 max-w-2xl mx-auto">
                acesse o canal no youtube para ver todos os vídeos sobre computação, 
                programação, ferramentas e dicas para desenvolvedores.
              </p>
              <a
                href="https://www.youtube.com/@g0tt_4rdi"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block border border-white text-white px-8 py-3 text-lg font-medium btn-secondary"
              >
                acessar canal →
              </a>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
