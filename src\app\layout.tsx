import type { Metada<PERSON> } from "next";
import { Source_Code_Pro } from "next/font/google";
import "./globals.css";

const sourceCodePro = Source_Code_Pro({
  variable: "--font-source-code-pro",
  subsets: ["latin"],
  weight: ["300", "400", "500", "600", "700"],
});

export const metadata: Metadata = {
  title: "g0tt",
  description: "canal de computação no youtube. softwares, ferramentas e recursos para desenvolvedores.",
  keywords: "programação, desenvolvimento, software, ferramentas, youtube, computação",
  authors: [{ name: "g0tt" }],
  icons: {
    icon: '/cartola.ico',
  },
  openGraph: {
    title: "g0tt",
    description: "canal de computação no youtube. softwares, ferramentas e recursos para desenvolvedores.",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="pt-BR">
      <body
        className={`${sourceCodePro.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
