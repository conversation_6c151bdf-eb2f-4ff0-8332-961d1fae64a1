@import "tailwindcss";

:root {
  --font-sans: var(--font-source-code-pro);
}

* {
  box-sizing: border-box;
  font-family: var(--font-source-code-pro), monospace !important;
}

body {
  background: #000000;
  color: #ffffff;
  font-family: var(--font-source-code-pro), monospace !important;
  line-height: 1.6;
}

/* Force Source Code Pro on all elements */
h1, h2, h3, h4, h5, h6, p, a, button, div, span, li, ul, ol {
  font-family: var(--font-source-code-pro), monospace !important;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
a:focus,
button:focus {
  outline: 2px solid #ffffff;
  outline-offset: 2px;
}

/* Selection styles */
::selection {
  background: #ffffff;
  color: #0a0a0a;
}

/* Disable text selection for most elements */
* {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Allow text selection for specific elements where needed */
p, span, div.prose, .selectable {
  user-select: text;
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
}

/* Smooth animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Apply animations to page elements */
.animate-fade-in {
  animation: fadeIn 300ms ease-out;
}

.animate-slide-in-up {
  animation: slideInUp 400ms ease-out;
}

.animate-slide-in-down {
  animation: slideInDown 300ms ease-out;
}

/* Smooth transitions for interactive elements */
a, button {
  transition: all 200ms ease-in-out;
}

/* Hover effects */
a:hover, button:hover {
  transform: translateY(-1px);
}

/* Specific hover effects for buttons */
.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(255, 255, 255, 0.1);
}

.btn-secondary:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(255, 255, 255, 0.1);
}
