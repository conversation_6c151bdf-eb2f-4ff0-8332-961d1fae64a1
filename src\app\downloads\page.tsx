import Link from "next/link";
import Image from "next/image";

export default function Downloads() {
  return (
    <div className="min-h-screen bg-black text-white">
      <div className="max-w-4xl mx-auto px-6 py-12 animate-fade-in">
        {/* Header */}
        <header className="mb-16 animate-slide-in-down">
          <Link href="/" className="text-sm text-gray-400 hover:text-white hover:underline mb-4 block">
            ← voltar ao início
          </Link>
          <h1 className="text-4xl font-bold mb-4">downloads</h1>
        </header>

        {/* Downloads */}
        <main className="space-y-8 animate-slide-in-up">
          <div className="border-l-4 border-white pl-6">
            <div className="flex items-center gap-4 mb-3">
              <Image
                src="/cartola.ico"
                alt="debloat pack"
                width={32}
                height={32}
                className="flex-shrink-0"
              />
              <h2 className="text-2xl font-semibold">debloat pack</h2>
            </div>
            <p className="text-gray-400 mb-4">
              pasta completa com ferramentas para otimização e limpeza do windows
            </p>
            <div className="flex flex-wrap gap-4">
              <a
                href="https://bit.ly/g0tt-debloat-pack"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block bg-white text-black px-6 py-3 font-medium btn-primary"
              >
                baixar arquivo →
              </a>
              <a
                href="https://www.youtube.com/watch?v=VP05HvGproo"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block border border-white text-white px-6 py-3 font-medium btn-secondary"
              >
                ver vídeo →
              </a>
            </div>
          </div>

          <div className="border-l-4 border-white pl-6">
            <div className="flex items-center gap-4 mb-3">
              <Image
                src="/debloat.ico"
                alt="autounattend.xml"
                width={32}
                height={32}
                className="flex-shrink-0"
              />
              <h2 className="text-2xl font-semibold">autounattend.xml</h2>
            </div>
            <p className="text-gray-400 mb-4">
              arquivo de configuração para instalação automatizada do windows
            </p>
            <div className="flex flex-wrap gap-4">
              <a
                href="https://bit.ly/autounattend-g0tt"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block bg-white text-black px-6 py-3 font-medium btn-primary"
              >
                baixar arquivo →
              </a>
              <a
                href="https://www.youtube.com/watch?v=VP05HvGproo"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block border border-white text-white px-6 py-3 font-medium btn-secondary"
              >
                ver vídeo →
              </a>
              <a
                href="https://schneegans.de/windows/unattend-generator/"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-block border border-white text-white px-6 py-3 font-medium btn-secondary"
              >
                crie o seu →
              </a>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}
